import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/questionnaire/controller/vertigo_questionnaire_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_footer.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_option_widgets.dart';
import 'package:gt_plus/utils/reusableWidgets/questionnaire_progress_indicator.dart';
import 'package:gt_plus/utils/reusableWidgets/questionnaire_navigation_buttons.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/models/questionnaire_models.dart';

class VertigoQuestionnaireView extends GetView<VertigoQuestionnaireController> {
  const VertigoQuestionnaireView({super.key});

  static const String routeName = "/VertigoQuestionnaireView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return _buildContent(context);
    });
  }

  Widget _buildContent(BuildContext context) {
    final questionnaireSet = controller.getQuestionnaireSet();
    if (questionnaireSet == null) {
      return const Center(
        child: Text('Failed to load questionnaire data'),
      );
    }

    return Obx(() {
      if (controller.allQuestions.isEmpty) {
        return const Center(
          child: Text('No questions available'),
        );
      }

      return Padding(
        padding: EdgeInsets.symmetric(horizontal: context.width * .04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            _buildHeader(context, questionnaireSet),
            const SizedBox(height: 6),
            _buildReadOnlyIndicator(),
            const SizedBox(height: 12),
            _buildProgressIndicator(),
            const SizedBox(height: 12),
            _buildDescription(context, questionnaireSet),
            const SizedBox(height: 16),
            Expanded(
              child: _buildCurrentQuestion(context),
            ),
            _buildNavigationButtons(context),
            _buildActionButtons(context),
            const SizedBox(height: 4),
          ],
        ),
      );
    });
  }

  Widget _buildHeader(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Text(
      questionnaireSet.label,
      style: const TextStyle(
        color: AppColors.charcoalBlue,
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildReadOnlyIndicator() {
    return Obx(() {
      if (!controller.isReadOnly.value) return const SizedBox.shrink();

      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.softGray.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.softGray),
        ),
        child: const Row(
          children: [
            Icon(Icons.info_outline, color: AppColors.charcoalBlue, size: 20),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                'This questionnaire has already been completed and is in read-only mode.',
                style: TextStyle(
                  color: AppColors.charcoalBlue,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildDescription(
      BuildContext context, QuestionnaireSet questionnaireSet) {
    if (questionnaireSet.description.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.charcoalBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        questionnaireSet.description,
        style: const TextStyle(
          color: AppColors.charcoalBlue,
          fontSize: 14,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildQuestion(BuildContext context,
      QuestionnaireQuestion questionnaire, int questionNumber) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.softGray.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$questionNumber. ${questionnaire.label}',
            style: const TextStyle(
              color: AppColors.charcoalBlue,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildQuestionOptions(context, questionnaire),
        ],
      ),
    );
  }

  Widget _buildQuestionOptions(
      BuildContext context, QuestionnaireQuestion questionnaire) {
    if (questionnaire.option?.type == 'radio') {
      return _buildRadioOptions(context, questionnaire);
    } else if (questionnaire.option?.type == 'checkbox') {
      return _buildCheckboxOptions(context, questionnaire);
    }
    return const SizedBox.shrink();
  }

  Widget _buildRadioOptions(
      BuildContext context, QuestionnaireQuestion questionnaire) {
    return Obx(() {
      final selectedValue = controller.getRadioAnswer(questionnaire.value);
      final isReadOnly = controller.isReadOnly.value;

      return ReusableRadioGroup(
        labels: questionnaire.option!.subOption!
            .map((option) => option.label)
            .toList(),
        values: questionnaire.option!.subOption!
            .map((option) => option.value)
            .toList(),
        selectedValue: selectedValue,
        isReadOnly: isReadOnly,
        useWrapLayout: false, // Use column layout for vertigo questionnaire
        onChanged: (value) {
          controller.updateRadioAnswer(questionnaire.value, value);
        },
      );
    });
  }

  Widget _buildCheckboxOptions(
      BuildContext context, QuestionnaireQuestion questionnaire) {
    return Obx(() {
      final selectedValues =
          controller.checkboxAnswers[questionnaire.value] ?? [];
      final isReadOnly = controller.isReadOnly.value;

      return ReusableCheckboxGroup(
        labels: questionnaire.option!.subOption!
            .map((option) => option.label)
            .toList(),
        values: questionnaire.option!.subOption!
            .map((option) => option.value)
            .toList(),
        selectedValues: selectedValues,
        isReadOnly: isReadOnly,
        onChanged: (newValues) {
          // Update the controller's checkbox answers
          controller.checkboxAnswers[questionnaire.value] = newValues;

          // Only update the answer if there are selected values
          // This prevents sending empty arrays to the API for optional questions
          if (newValues.isNotEmpty) {
            controller.updateAnswer(questionnaire.value, newValues);
          } else {
            // Remove the answer from the base controller if no options are selected
            controller.removeAnswer(questionnaire.value);
          }
        },
      );
    });
  }

  Widget _buildActionButtons(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Show Reset and Edit buttons when in read-only mode
        return Row(
          children: [
            ReusableButton(
              width: context.width * .22,
              title: 'Reset',
              color: Colors.red,
              borderColor: Colors.red,
              onTap: () {
                controller.resetForm();
              },
            ),
            const SizedBox(width: 16),
            ReusableButton(
              width: context.width * .22,
              title: 'Edit',
              onTap: () {
                controller.enableEditMode();
              },
            ),
          ],
        );
      } else {
        return const SizedBox
            .shrink(); // Navigation buttons handle submission now
      }
    });
  }

  Widget _buildProgressIndicator() {
    return Obx(() {
      return QuestionnaireProgressIndicator(
        progressPercentage: controller.getProgressPercentage(),
        currentProgress: controller.getCurrentQuestionProgress(),
        title: 'Questionnaire Progress',
      );
    });
  }

  Widget _buildCurrentQuestion(BuildContext context) {
    return Obx(() {
      final currentQuestion = controller.getCurrentQuestion();
      if (currentQuestion == null) {
        return const Center(
          child: Text('No question available'),
        );
      }

      final questionNumber = controller.currentQuestionIndex.value + 1;

      return SingleChildScrollView(
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Question $questionNumber',
                style: TextStyle(
                  color: AppColors.charcoalBlue.withValues(alpha: 0.7),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                currentQuestion.title.isNotEmpty
                    ? currentQuestion.title
                    : currentQuestion.label,
                style: const TextStyle(
                  color: AppColors.charcoalBlue,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  height: 1.5,
                ),
                maxLines: null, // Allow unlimited lines
                overflow: TextOverflow.visible, // Show all text
              ),
              const SizedBox(height: 24),
              _buildQuestionOptions(context, currentQuestion),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildNavigationButtons(BuildContext context) {
    return Obx(() {
      return QuestionnaireNavigationButtons(
        hasPrevious: controller.hasPreviousQuestion(),
        hasNext: controller.hasNextQuestion(),
        onPrevious: controller.goToPreviousQuestion,
        onNext: controller.goToNextQuestion,
        onSubmit: controller.submitQuestionnaire,
        isSubmitting: controller.isSubmitting.value,
        isReadOnly: controller.isReadOnly.value,
        showSubmit:
            !controller.hasNextQuestion() && !controller.isReadOnly.value,
      );
    });
  }
}

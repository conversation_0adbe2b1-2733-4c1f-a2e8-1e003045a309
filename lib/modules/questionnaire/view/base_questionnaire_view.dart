import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/questionnaire/controller/base_questionnaire_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_footer.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/reusableWidgets/questionnaire_progress_indicator.dart';
import 'package:gt_plus/utils/reusableWidgets/questionnaire_navigation_buttons.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/models/questionnaire_models.dart';

/// Base class for questionnaire views to reduce code duplication
/// Provides common layout structure and reusable methods
abstract class BaseQuestionnaireView<T extends BaseQuestionnaireController>
    extends GetView<T> {
  const BaseQuestionnaireView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      resizeToAvoidBottomInset: true, // Allow resizing when keyboard appears
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return _buildContent(context);
    });
  }

  Widget _buildContent(BuildContext context) {
    final questionnaireSet = controller.getQuestionnaireSet();
    if (questionnaireSet == null) {
      return const Center(
        child: Text('Failed to load questionnaire data'),
      );
    }

    return Obx(() {
      if (controller.allQuestions.isEmpty) {
        return const Center(
          child: Text('No questions available'),
        );
      }

      return GestureDetector(
        onTap: () =>
            FocusScope.of(context).unfocus(), // Dismiss keyboard on tap outside
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * .04),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 6),
              _buildHeader(context, questionnaireSet),
              const SizedBox(height: 4),
              _buildReadOnlyIndicator(),
              const SizedBox(height: 2),
              _buildProgressIndicator(),
              const SizedBox(height: 2),
              _buildDescription(context, questionnaireSet),
              const SizedBox(height: 4),
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final keyboardHeight =
                        MediaQuery.of(context).viewInsets.bottom;
                    return SingleChildScrollView(
                      keyboardDismissBehavior:
                          ScrollViewKeyboardDismissBehavior.onDrag,
                      padding:
                          EdgeInsets.only(bottom: keyboardHeight > 0 ? 20 : 4),
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          minHeight: constraints.maxHeight -
                              (keyboardHeight > 0 ? 20 : 4),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            buildCurrentQuestion(context),
                            const SizedBox(height: 4),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              _buildNavigationButtons(context),
              _buildActionButtons(context),
            ],
          ),
        ),
      );
    });
  }

  // Common reusable methods
  Widget _buildHeader(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Text(
      questionnaireSet.label,
      style: const TextStyle(
        color: AppColors.charcoalBlue,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildReadOnlyIndicator() {
    return Obx(() {
      if (!controller.isReadOnly.value) return const SizedBox.shrink();

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          border: Border.all(color: Colors.orange.shade200),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(Icons.info_outline, color: Colors.orange.shade600, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'This questionnaire has been completed and is in read-only mode.',
                style: TextStyle(
                  color: Colors.orange.shade700,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildProgressIndicator() {
    return Obx(() {
      return QuestionnaireProgressIndicator(
        progressPercentage: controller.getProgressPercentage(),
        currentProgress: controller.getCurrentQuestionProgress(),
        title: 'Questionnaire Progress',
      );
    });
  }

  Widget _buildDescription(
      BuildContext context, QuestionnaireSet questionnaireSet) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.charcoalBlue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        questionnaireSet.description,
        style: const TextStyle(
          color: AppColors.charcoalBlue,
          fontSize: 11,
          height: 1.3,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildNavigationButtons(BuildContext context) {
    return Obx(() {
      return QuestionnaireNavigationButtons(
        hasPrevious: controller.hasPreviousQuestion(),
        hasNext: controller.hasNextQuestion(),
        onPrevious: controller.goToPreviousQuestion,
        onNext: controller.goToNextQuestion,
        onSubmit: controller.submitQuestionnaire,
        isSubmitting: controller.isSubmitting.value,
        isReadOnly: controller.isReadOnly.value,
        showSubmit:
            !controller.hasNextQuestion() && !controller.isReadOnly.value,
      );
    });
  }

  Widget _buildActionButtons(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Show Reset and Edit buttons when in read-only mode
        return Row(
          children: [
            ReusableButton(
              width: context.width * .22,
              title: 'Reset',
              color: Colors.red,
              borderColor: Colors.red,
              onTap: () {
                controller.resetForm();
              },
            ),
            const SizedBox(width: 16),
            ReusableButton(
              width: context.width * .22,
              title: 'Edit',
              onTap: () {
                controller.enableEditMode();
              },
            ),
          ],
        );
      } else {
        return const SizedBox
            .shrink(); // Navigation buttons handle submission now
      }
    });
  }

  // Abstract method that subclasses must implement
  Widget buildCurrentQuestion(BuildContext context);
}

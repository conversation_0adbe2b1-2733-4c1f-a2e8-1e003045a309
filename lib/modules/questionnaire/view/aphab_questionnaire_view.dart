import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/questionnaire/controller/aphab_questionnaire_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_footer.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_option_widgets.dart';
import 'package:gt_plus/utils/reusableWidgets/questionnaire_progress_indicator.dart';
import 'package:gt_plus/utils/reusableWidgets/questionnaire_navigation_buttons.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/models/questionnaire_models.dart';

class APHABQuestionnaireView extends GetView<APHABQuestionnaireController> {
  const APHABQuestionnaireView({super.key});

  static const String routeName = "/APHABQuestionnaireView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return _buildContent(context);
    });
  }

  Widget _buildContent(BuildContext context) {
    final questionnaireSet = controller.getQuestionnaireSet();
    if (questionnaireSet == null) {
      return const Center(
        child: Text('Failed to load questionnaire data'),
      );
    }

    return Obx(() {
      if (controller.allQuestions.isEmpty) {
        return const Center(
          child: Text('No questions available'),
        );
      }

      return Padding(
        padding: EdgeInsets.symmetric(horizontal: context.width * .04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            _buildHeader(context, questionnaireSet),
            const SizedBox(height: 6),
            _buildReadOnlyIndicator(),
            _buildProgressIndicator(),
            const SizedBox(height: 12),
            _buildDescription(context, questionnaireSet),
            const SizedBox(height: 16),
            Expanded(
              child: _buildCurrentQuestion(context),
            ),
            _buildNavigationButtons(context),
            _buildActionButtons(context),
            const SizedBox(height: 4),
          ],
        ),
      );
    });
  }

  Widget _buildHeader(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          questionnaireSet.label,
          style: const TextStyle(
            color: AppColors.charcoalBlue,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildReadOnlyIndicator() {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Read-Only Mode Indicator
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            border: Border.all(color: Colors.blue.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lock_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Read-Only Mode: This questionnaire has been completed. Use 'Edit' to make changes or 'Reset' to start over.",
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else if (controller.isFormCompleted.value &&
          !controller.isReadOnly.value) {
        // Edit Mode Indicator (for previously completed forms)
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            border: Border.all(color: Colors.orange.shade200),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Edit Mode: You are now editing a previously completed questionnaire. Changes will be saved when you submit.",
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return const SizedBox.shrink();
      }
    });
  }

  Widget _buildDescription(
      BuildContext context, QuestionnaireSet questionnaireSet) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.charcoalBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        questionnaireSet.description,
        style: const TextStyle(
          color: AppColors.charcoalBlue,
          fontSize: 14,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildAPHABAnswerWidget(BuildContext context,
      QuestionnaireSection section, QuestionnaireSet questionnaireSet) {
    // APHAB uses options at the questionnaire level
    if (questionnaireSet.options != null &&
        questionnaireSet.options!.isNotEmpty) {
      return _buildAPHABRadioAnswer(
          context, section, questionnaireSet.options!);
    }

    return const SizedBox.shrink();
  }

  Widget _buildAPHABRadioAnswer(BuildContext context,
      QuestionnaireSection section, List<QuestionnaireOption> options) {
    return Obx(() {
      final selectedValue = controller.getAnswer(section.value) as String?;
      final isReadOnly = controller.isReadOnly.value;

      return ReusableRadioGroup(
        labels: options.map((option) => option.label).toList(),
        values: options.map((option) => option.value).toList(),
        selectedValue: selectedValue,
        isReadOnly: isReadOnly,
        useWrapLayout: false, // Use column layout for APHAB
        onChanged: (value) {
          controller.updateAnswer(section.value, value);
        },
      );
    });
  }

  Widget _buildProgressIndicator() {
    return Obx(() {
      return QuestionnaireProgressIndicator(
        progressPercentage: controller.getProgressPercentage(),
        currentProgress: controller.getCurrentQuestionProgress(),
        title: 'Questionnaire Progress',
      );
    });
  }

  Widget _buildCurrentQuestion(BuildContext context) {
    return Obx(() {
      final currentQuestion = controller.getCurrentQuestion();
      if (currentQuestion == null) {
        return const Center(
          child: Text('No question available'),
        );
      }

      final questionNumber = controller.currentQuestionIndex.value + 1;

      return SingleChildScrollView(
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Question $questionNumber',
                style: TextStyle(
                  color: AppColors.charcoalBlue.withValues(alpha: 0.7),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                currentQuestion.title,
                style: const TextStyle(
                  color: AppColors.charcoalBlue,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  height: 1.5,
                ),
                maxLines: null, // Allow unlimited lines
                overflow: TextOverflow.visible, // Show all text
              ),
              const SizedBox(height: 24),
              _buildAPHABQuestionAnswerWidget(context, currentQuestion),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildNavigationButtons(BuildContext context) {
    return Obx(() {
      return QuestionnaireNavigationButtons(
        hasPrevious: controller.hasPreviousQuestion(),
        hasNext: controller.hasNextQuestion(),
        onPrevious: controller.goToPreviousQuestion,
        onNext: controller.goToNextQuestion,
        onSubmit: controller.submitQuestionnaire,
        isSubmitting: controller.isSubmitting.value,
        isReadOnly: controller.isReadOnly.value,
        showSubmit:
            !controller.hasNextQuestion() && !controller.isReadOnly.value,
      );
    });
  }

  Widget _buildActionButtons(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        // Show Reset and Edit buttons when in read-only mode
        return Row(
          children: [
            ReusableButton(
              width: context.width * .22,
              title: 'Reset',
              color: Colors.red,
              borderColor: Colors.red,
              onTap: () {
                controller.resetForm();
              },
            ),
            const SizedBox(width: 16),
            ReusableButton(
              width: context.width * .22,
              title: 'Edit',
              onTap: () {
                controller.enableEditMode();
              },
            ),
          ],
        );
      } else {
        return const SizedBox
            .shrink(); // Navigation buttons handle submission now
      }
    });
  }

  Widget _buildAPHABQuestionAnswerWidget(
      BuildContext context, QuestionnaireQuestion question) {
    final questionnaireSet = controller.getQuestionnaireSet()!;

    // APHAB uses options at the questionnaire level
    if (questionnaireSet.options != null &&
        questionnaireSet.options!.isNotEmpty) {
      return _buildAPHABRadioAnswerForQuestion(
          context, question, questionnaireSet.options!);
    }

    return const SizedBox.shrink();
  }

  Widget _buildAPHABRadioAnswerForQuestion(BuildContext context,
      QuestionnaireQuestion question, List<QuestionnaireOption> options) {
    return Obx(() {
      final selectedValue = controller.getAnswer(question.value) as String?;
      final isReadOnly = controller.isReadOnly.value;

      return ReusableRadioGroup(
        labels: options.map((option) => option.label).toList(),
        values: options.map((option) => option.value).toList(),
        selectedValue: selectedValue,
        isReadOnly: isReadOnly,
        useWrapLayout: false, // Use column layout for APHAB
        onChanged: (value) {
          controller.updateAnswer(question.value, value);
        },
      );
    });
  }
}

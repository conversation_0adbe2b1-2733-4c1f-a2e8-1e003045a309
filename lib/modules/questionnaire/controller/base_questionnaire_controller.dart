import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/enums/questionnaire_type_enum.dart';
import 'package:gt_plus/models/questionnaire_models.dart';
import 'package:gt_plus/services/api_service.dart';
import 'package:gt_plus/services/remoteConfig/firebase_remote_config_service.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import '../../meta/view/meta_view.dart';
import '../../meta/controller/meta_controller.dart';

abstract class BaseQuestionnaireController extends GetxController {
  final RxBool isLoading = false.obs;
  final RxBool isInitializing = true.obs;
  final RxBool isSubmitting = false.obs;

  // Read-only mode management
  final RxBool isReadOnly = false.obs;
  final RxBool isFormCompleted = false.obs;

  final ApiService _apiService = ApiService();
  final FirebaseRemoteConfigService _remoteConfigService =
      FirebaseRemoteConfigService();

  // Questionnaire data
  QuestionnaireData? questionnaireData;
  final RxMap<String, dynamic> answers = <String, dynamic>{}.obs;

  // Navigation state
  final RxInt currentQuestionIndex = 0.obs;
  final RxList<QuestionnaireQuestion> allQuestions =
      <QuestionnaireQuestion>[].obs;

  // Abstract properties that must be implemented by subclasses
  QuestionnaireType get questionnaireType;
  String get questionnaireKey; // e.g., "setOne", "setTwo", etc.
  String get routeName; // Route name for completion status checking

  @override
  void onInit() {
    super.onInit();
    initializeQuestionnaire();
  }

  Future<void> initializeQuestionnaire() async {
    try {
      isInitializing.value = true;

      // Check completion status first
      await _checkCompletionStatus();

      await loadQuestionnaireData();
      await loadSavedAnswers();
    } catch (e) {
      debugPrint('Error initializing questionnaire: $e');
      reusableSnackBar(message: 'Failed to load questionnaire data');
    } finally {
      isInitializing.value = false;
    }
  }

  Future<void> loadQuestionnaireData() async {
    try {
      final jsonString = _remoteConfigService.getQuestionnairesData();
      if (jsonString.isEmpty) {
        throw Exception('No questionnaire data found in remote config');
      }

      final jsonData = jsonDecode(jsonString);
      final audiologyData = jsonData['Audiology']?['Audiology'];

      if (audiologyData != null) {
        questionnaireData = QuestionnaireData.fromJson(audiologyData);
        _initializeQuestionsList();
      } else {
        throw Exception('Audiology questionnaire data not found');
      }
    } catch (e) {
      debugPrint('Error loading questionnaire data: $e');
      rethrow;
    }
  }

  // Initialize the flat list of all questions for navigation
  void _initializeQuestionsList() {
    allQuestions.clear();
    final questionnaireSet = getQuestionnaireSet();

    if (questionnaireSet?.data != null) {
      for (final section in questionnaireSet!.data!) {
        // Check if this section has questions inside it (like RHHI, TFI)
        if (section.questions != null && section.questions!.isNotEmpty) {
          allQuestions.addAll(section.questions!);
        }
        // For APHAB and similar structures, the section itself IS the question
        else {
          // Create a question from the section data
          final question = QuestionnaireQuestion(
            title: section.title ?? section.label,
            value: section.value,
            minScale: section.minScale,
            maxScale: section.maxScale,
            stepScale: section.stepScale,
            startLabel: section.startLabel,
            endLabel: section.endLabel,
            scale: section.scale,
          );
          allQuestions.add(question);
        }
      }
    }

    // Also check for questionnaires field (used by Vertigo)
    if (questionnaireSet?.questionnaires != null) {
      allQuestions.addAll(questionnaireSet!.questionnaires!);
    }

    // Reset to first question
    currentQuestionIndex.value = 0;
  }

  Future<void> loadSavedAnswers() async {
    try {
      // Try to load previously saved answers from unified questionnaire API
      final response = await _apiService.getQuestionnaire(
        types: [questionnaireType.apiFieldName],
      );

      if (response != null && response['answers'] != null) {
        // API returns data directly, not nested under 'data'
        answers.value = Map<String, dynamic>.from(response['answers']);
        debugPrint('Loaded saved answers: ${response['answers']}');
      }
    } catch (e) {
      debugPrint('Error loading saved answers: $e');
      // Continue without saved answers
    }
  }

  void updateAnswer(String questionId, dynamic value) {
    // Round percentage values to 2 decimal places if it's a double
    if (value is double) {
      // Check if this is a percentage question by looking at the question's scale
      final questionnaireSet = getQuestionnaireSet();
      if (questionnaireSet?.data != null) {
        for (final section in questionnaireSet!.data!) {
          if (section.questions != null) {
            for (final question in section.questions!) {
              if (question.value == questionId &&
                  question.minScale == 0 &&
                  question.maxScale == 100) {
                // This is a percentage question, round to 2 decimal places
                value = double.parse(value.toStringAsFixed(2));
                break;
              }
            }
          }
        }
      }
    }

    answers[questionId] = value;
    answers.refresh();
  }

  dynamic getAnswer(String questionId) {
    return answers[questionId];
  }

  void removeAnswer(String questionId) {
    answers.remove(questionId);
    answers.refresh();
  }

  bool isQuestionAnswered(String questionId) {
    final answer = answers[questionId];
    if (answer == null) return false;
    if (answer is String) return answer.isNotEmpty;
    if (answer is List) return answer.isNotEmpty;
    return true;
  }

  // Validate current question before navigation
  bool validateCurrentQuestion() {
    final currentQuestion = getCurrentQuestion();
    if (currentQuestion == null) return true;

    // Check if current question is answered
    if (!isQuestionAnswered(currentQuestion.value)) {
      reusableSnackBar(
          message: 'Please answer the current question before proceeding');
      return false;
    }

    return true;
  }

  QuestionnaireSet? getQuestionnaireSet() {
    return questionnaireData?.questionnaires[questionnaireKey];
  }

  bool validateAnswers() {
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet == null) return false;

    // Check if all required questions are answered
    if (questionnaireSet.data != null) {
      for (final section in questionnaireSet.data!) {
        if (section.questions != null) {
          for (final question in section.questions!) {
            if (!isQuestionAnswered(question.value)) {
              reusableSnackBar(message: 'Please answer all questions');
              return false;
            }
          }
        }
      }
    }

    return true;
  }

  Future<void> submitQuestionnaire() async {
    if (!validateAnswers()) return;

    try {
      isSubmitting.value = true;

      final response = QuestionnaireResponse(
        questionnaireType: questionnaireType.apiFieldName,
        answers: Map<String, dynamic>.from(answers),
        completedAt: DateTime.now(),
      );

      final success = await _apiService.postQuestionnaire(
        types: [questionnaireType.apiFieldName],
        data: response.toJson(),
      );

      if (success) {
        reusableSnackBar(
            message: 'Questionnaire submitted successfully', isForError: false);
        Get.offAllNamed(MetaView.routeName);
      } else {
        reusableSnackBar(message: 'Failed to submit questionnaire');
      }
    } catch (e) {
      debugPrint('Error submitting questionnaire: $e');
      reusableSnackBar(message: 'Error submitting questionnaire');
    } finally {
      isSubmitting.value = false;
    }
  }

  // Navigation methods
  void goToNextQuestion() {
    // Validate current question before moving to next
    if (!validateCurrentQuestion()) {
      return; // Don't navigate if current question is not valid
    }

    if (currentQuestionIndex.value < allQuestions.length - 1) {
      currentQuestionIndex.value++;
    }
  }

  void goToPreviousQuestion() {
    if (currentQuestionIndex.value > 0) {
      currentQuestionIndex.value--;
    }
  }

  void goToQuestion(int index) {
    if (index >= 0 && index < allQuestions.length) {
      currentQuestionIndex.value = index;
    }
  }

  // Get current question
  QuestionnaireQuestion? getCurrentQuestion() {
    if (allQuestions.isEmpty ||
        currentQuestionIndex.value >= allQuestions.length) {
      return null;
    }
    return allQuestions[currentQuestionIndex.value];
  }

  // Check if there's a next question
  bool hasNextQuestion() {
    return currentQuestionIndex.value < allQuestions.length - 1;
  }

  // Check if there's a previous question
  bool hasPreviousQuestion() {
    return currentQuestionIndex.value > 0;
  }

  // Helper method to get progress percentage
  double getProgressPercentage() {
    if (allQuestions.isEmpty) return 0.0;

    int answeredQuestions = 0;
    for (final question in allQuestions) {
      if (isQuestionAnswered(question.value)) {
        answeredQuestions++;
      }
    }

    return answeredQuestions / allQuestions.length;
  }

  // Get current question progress (1-based)
  String getCurrentQuestionProgress() {
    if (allQuestions.isEmpty) return "0/0";
    return "${currentQuestionIndex.value + 1}/${allQuestions.length}";
  }

  // Get the section that contains a specific question
  QuestionnaireSection? getSectionForQuestion(QuestionnaireQuestion question) {
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.data == null) return null;

    for (final section in questionnaireSet!.data!) {
      if (section.questions != null) {
        for (final q in section.questions!) {
          if (q.value == question.value) {
            return section;
          }
        }
      }
    }
    return null;
  }

  // Method to enable read-only mode
  void enableReadOnlyMode() {
    debugPrint("Enabling read-only mode for ${questionnaireType.title}");
    isReadOnly.value = true;
    isFormCompleted.value = true;
  }

  // Method to enable edit mode
  void enableEditMode() {
    isReadOnly.value = false;
  }

  // Method to reset form
  void resetForm() {
    answers.clear();
    isReadOnly.value = false;
    isFormCompleted.value = false;
    currentQuestionIndex.value = 0;

    // Call any subclass-specific reset logic
    onFormReset();
  }

  // Virtual method for subclasses to override for custom reset logic
  void onFormReset() {
    // Default implementation does nothing
    // Subclasses can override this for custom reset behavior
  }

  // Check completion status from MetaController
  Future<void> _checkCompletionStatus() async {
    try {
      final metaController = Get.find<MetaController>();
      final isCompleted =
          metaController.moduleCompletionStatus[routeName] ?? false;
      final hasCompletedGTPlusOnce =
          metaController.hasPatientCompletedGTPlusOnce();

      debugPrint("${questionnaireType.title} route name: $routeName");
      debugPrint("${questionnaireType.title} completion status: $isCompleted");
      debugPrint("Has patient completed GT+ once: $hasCompletedGTPlusOnce");

      if (isCompleted || hasCompletedGTPlusOnce) {
        debugPrint("Setting ${questionnaireType.title} to read-only mode");
        enableReadOnlyMode();
      }
    } catch (e) {
      debugPrint(
          'Error checking completion status for ${questionnaireType.title}: $e');
      // Continue without setting read-only mode if there's an error
    }
  }
}

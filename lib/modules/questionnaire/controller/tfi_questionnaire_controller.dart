import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/enums/questionnaire_type_enum.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import 'package:gt_plus/models/questionnaire_models.dart';
import 'base_questionnaire_controller.dart';
import '../view/tfi_questionnaire_view.dart';

class TFIQuestionnaireController extends BaseQuestionnaireController {
  @override
  QuestionnaireType get questionnaireType => QuestionnaireType.tfiQuest;

  @override
  String get questionnaireKey =>
      'setThree'; // TFI is in setThree based on the demo data

  @override
  String get routeName => TFIQuestionnaireView.routeName;

  // Observable for tracking meta question answer
  final Rxn<bool> metaQuestionAnswer = Rxn<bool>();

  // Observable for showing detailed questions
  final RxBool showDetailedQuestions = false.obs;

  // Text controllers for "other" text fields
  final Map<String, TextEditingController> otherTextControllers = {};

  // Track which questions have been actually answered by user (not just initialized)
  final RxSet<String> userAnsweredQuestions = <String>{}.obs;

  @override
  void onClose() {
    // Dispose of text controllers
    for (final controller in otherTextControllers.values) {
      controller.dispose();
    }
    otherTextControllers.clear();
    super.onClose();
  }

  // Get or create a text controller for an "other" text field
  TextEditingController getOtherTextController(String key) {
    if (!otherTextControllers.containsKey(key)) {
      final controller = TextEditingController();
      // Add listener to sync with answers
      controller.addListener(() {
        final currentAnswer = getAnswer(key) as String? ?? '';
        if (currentAnswer != controller.text) {
          // Use direct super call and manually track user interaction
          super.updateAnswer(key, controller.text);
          userAnsweredQuestions.add(key);
        }
      });
      otherTextControllers[key] = controller;

      // Set initial text from saved answer
      final savedAnswer = getAnswer(key) as String? ?? '';
      controller.text = savedAnswer;
    }
    return otherTextControllers[key]!;
  }

  @override
  void updateAnswer(String questionId, dynamic value) {
    super.updateAnswer(questionId, value);
    // Track user interactions
    userAnsweredQuestions.add(questionId);
  }

  // Initialize answers with defaults but don't mark as user-answered
  void _initializeAnswerWithDefault(String questionId, dynamic value) {
    super.updateAnswer(questionId, value);
    // Don't add to userAnsweredQuestions since this is just initialization
  }

  void updateMetaAnswer(bool hasExperienceTinnitus) {
    metaQuestionAnswer.value = hasExperienceTinnitus;
    // Use direct super call to avoid adding to userAnsweredQuestions twice
    super.updateAnswer('metaQuest', hasExperienceTinnitus);
    userAnsweredQuestions.add('metaQuest');

    if (hasExperienceTinnitus) {
      // User has tinnitus, show detailed questions
      showDetailedQuestions.value = true;
      _initializeDetailedQuestionsWithDefaults();
    } else {
      // User doesn't have tinnitus, hide detailed questions and clear any previous answers
      showDetailedQuestions.value = false;
      _clearDetailedAnswers();
    }
  }

  void _clearDetailedAnswers() {
    // Clear all answers except the meta question answer
    final metaAnswer = answers['metaQuest'];
    answers.clear();
    answers['metaQuest'] = metaAnswer;

    // Clear user answered questions except meta question
    userAnsweredQuestions.clear();
    if (metaQuestionAnswer.value != null) {
      userAnsweredQuestions.add('metaQuest');
    }

    // Clear all text controllers
    for (final controller in otherTextControllers.values) {
      controller.clear();
    }

    // Also clear any metalist answers
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.metaAnswer != null) {
      final yesMetaAnswer = questionnaireSet!.metaAnswer!.firstWhere(
        (metaAnswer) => metaAnswer.value == true,
        orElse: () => questionnaireSet.metaAnswer!.first,
      );

      if (yesMetaAnswer.metalist != null) {
        for (final metaList in yesMetaAnswer.metalist!) {
          answers.remove(metaList.value);
          // Also remove the "other" text field if it exists
          answers.remove('${metaList.value}_other_text');
        }
      }
    }

    answers.refresh();
  }

  void _initializeDetailedQuestionsWithDefaults() {
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.data == null) {
      return;
    }

    // Initialize all questions with default values if they don't already have answers
    for (final section in questionnaireSet!.data!) {
      if (section.questions != null) {
        for (final question in section.questions!) {
          // Only initialize if the question doesn't already have an answer
          if (getAnswer(question.value) == null) {
            double defaultValue;

            // Use the question's minScale if available, otherwise use 0
            if (question.minScale != null) {
              defaultValue = question.minScale!.toDouble();
            } else {
              defaultValue = 0.0;
            }

            // Round percentage values to 2 decimal places
            if (question.minScale == 0 && question.maxScale == 100) {
              defaultValue = double.parse(defaultValue.toStringAsFixed(2));
            }

            _initializeAnswerWithDefault(question.value, defaultValue);
          }
        }
      }
    }
  }

  @override
  bool validateCurrentQuestion() {
    // If we're on the meta question, check if it's answered
    if (currentQuestionIndex.value == 0) {
      if (metaQuestionAnswer.value == null) {
        reusableSnackBar(
            message: 'Please answer the tinnitus experience question');
        return false;
      }
      return true;
    }

    // If user doesn't have tinnitus, they can navigate freely after meta question
    if (metaQuestionAnswer.value == false) {
      return true;
    }

    // For other questions, use the default validation
    return super.validateCurrentQuestion();
  }

  @override
  bool validateAnswers() {
    // First check if meta question is answered
    if (metaQuestionAnswer.value == null) {
      reusableSnackBar(
          message: 'Please answer the tinnitus experience question');
      return false;
    }

    // If user doesn't have tinnitus, only meta question needs to be answered
    if (metaQuestionAnswer.value == false) {
      return true;
    }

    // If user has tinnitus, validate metalist questions first
    if (!_validateMetaListQuestions()) {
      return false;
    }

    // If user has tinnitus, validate all detailed questions
    return super.validateAnswers();
  }

  bool _validateMetaListQuestions() {
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.metaAnswer == null) return true;

    // Find the YES metaAnswer
    final yesMetaAnswer = questionnaireSet!.metaAnswer!.firstWhere(
      (metaAnswer) => metaAnswer.value == true,
      orElse: () => questionnaireSet.metaAnswer!.first,
    );

    if (yesMetaAnswer.metalist == null || yesMetaAnswer.metalist!.isEmpty) {
      return true;
    }

    // Validate each metalist question
    for (final metaList in yesMetaAnswer.metalist!) {
      final answer = getAnswer(metaList.value);

      if (metaList.options.type == 'checkbox') {
        // For checkbox, ensure at least one option is selected
        if (answer == null || (answer is List && answer.isEmpty)) {
          reusableSnackBar(
              message:
                  'Please select at least one option for: ${metaList.label}');
          return false;
        }

        // If "other" is selected, validate that the text field is filled
        if (answer is List && answer.contains('other')) {
          final otherText =
              getAnswer('${metaList.value}_other_text') as String?;
          if (otherText == null || otherText.trim().isEmpty) {
            reusableSnackBar(
                message:
                    'Please specify the type of sound when selecting "Other"');
            return false;
          }
        }
      } else if (metaList.options.type == 'radio') {
        // For radio, ensure an option is selected
        if (answer == null || answer.toString().isEmpty) {
          reusableSnackBar(
              message: 'Please select an option for: ${metaList.label}');
          return false;
        }
      }
    }

    return true;
  }

  @override
  Future<void> loadSavedAnswers() async {
    await super.loadSavedAnswers();

    // Load saved meta answer
    final savedMetaAnswer = getAnswer('metaQuest');
    if (savedMetaAnswer != null && savedMetaAnswer is bool) {
      metaQuestionAnswer.value = savedMetaAnswer;
      showDetailedQuestions.value = savedMetaAnswer;
      userAnsweredQuestions
          .add('metaQuest'); // Mark meta question as user-answered

      // If user previously answered YES, initialize detailed questions with defaults
      if (savedMetaAnswer == true) {
        _initializeDetailedQuestionsWithDefaults();

        // Mark questions that have non-default answers as user-answered
        _markNonDefaultAnswersAsUserAnswered();
      }
    }

    // Initialize TFI questions list
    _initializeTFIQuestionsList();
  }

  void _markNonDefaultAnswersAsUserAnswered() {
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.data == null) return;

    for (final section in questionnaireSet!.data!) {
      if (section.questions != null) {
        for (final question in section.questions!) {
          final answer = getAnswer(question.value);
          if (answer != null) {
            double defaultValue = question.minScale?.toDouble() ?? 0.0;

            // If answer is different from default, mark as user-answered
            if (answer != defaultValue) {
              userAnsweredQuestions.add(question.value);
            }
          }
        }
      }
    }

    // Also check metalist questions
    if (questionnaireSet.metaAnswer != null) {
      final yesMetaAnswer = questionnaireSet.metaAnswer!.firstWhere(
        (metaAnswer) => metaAnswer.value == true,
        orElse: () => questionnaireSet.metaAnswer!.first,
      );

      if (yesMetaAnswer.metalist != null) {
        for (final metaList in yesMetaAnswer.metalist!) {
          final answer = getAnswer(metaList.value);
          if (answer != null &&
              ((answer is List && answer.isNotEmpty) ||
                  (answer is String && answer.isNotEmpty))) {
            userAnsweredQuestions.add(metaList.value);
          }
        }
      }
    }
  }

  // Custom question initialization to handle meta question
  void _initializeTFIQuestionsList() {
    allQuestions.clear();

    // For TFI, we need to handle the meta question and detailed questions separately
    // The meta question is always first, then detailed questions if user has tinnitus

    // Add meta question as a virtual question
    final questionnaireSet = getQuestionnaireSet();
    if (questionnaireSet?.metaQuest != null) {
      // Create a virtual question for the meta question
      final metaQuestion = QuestionnaireQuestion(
        title: questionnaireSet!.metaQuest!,
        value: 'metaQuest',
      );
      allQuestions.add(metaQuestion);
    }

    // Add detailed questions if they should be shown
    if (showDetailedQuestions.value && questionnaireSet?.data != null) {
      for (final section in questionnaireSet!.data!) {
        if (section.questions != null) {
          allQuestions.addAll(section.questions!);
        }
      }
    }

    // Reset to first question
    currentQuestionIndex.value = 0;
  }

  // Override the original updateMetaAnswer to refresh questions
  void updateMetaAnswerWithNavigation(bool hasExperienceTinnitus) {
    // Call the original method
    updateMetaAnswer(hasExperienceTinnitus);

    // Reinitialize questions list
    _initializeTFIQuestionsList();
  }

  @override
  double getProgressPercentage() {
    if (allQuestions.isEmpty) return 0.0;

    int answeredQuestions = 0;
    for (final question in allQuestions) {
      // For meta question, check if it's answered
      if (question.value == 'metaQuest') {
        if (metaQuestionAnswer.value != null) {
          answeredQuestions++;
        }
      } else {
        // For other questions, only count if user has actually interacted with them
        if (userAnsweredQuestions.contains(question.value)) {
          answeredQuestions++;
        }
      }
    }

    return answeredQuestions / allQuestions.length;
  }

  @override
  void onFormReset() {
    // Reset TFI-specific state
    metaQuestionAnswer.value = null;
    showDetailedQuestions.value = false;
    userAnsweredQuestions.clear();

    // Clear all text controllers
    for (final controller in otherTextControllers.values) {
      controller.clear();
    }

    // Reinitialize questions list
    _initializeTFIQuestionsList();
  }
}

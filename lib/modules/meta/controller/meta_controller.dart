import 'package:get/get.dart';
import 'package:gt_plus/models/meta_data_model.dart';
import 'package:gt_plus/models/basic_details_model.dart';
import 'package:gt_plus/modules/BleFlutter/ble_flutter_controller.dart';
import 'package:gt_plus/modules/ppg/controller/ppg_controller.dart';
import 'package:gt_plus/services/analytics/events.dart';

import 'package:gt_plus/services/api_service.dart';
import 'package:gt_plus/services/prefs_service.dart';
import 'package:gt_plus/utils/reusableWidgets/resusable_snackbar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_dialog.dart';
import 'package:gt_plus/utils/helpers/image_name_helper.dart';
import 'package:flutter/material.dart';
import '../../audiometryReportDataEntry/view/audiometry_report_data_entry_view.dart';
import '../../basicDetails/view/basic_detail_view.dart';
import '../../bloodPressure/view/blood_pressure_reading_view.dart';
import '../../cognivueReportDataEntry/view/cognivue_report_data_entry_view.dart';
import '../../extraPatientData/view/extra_patient_data_view.dart';
import '../../speechInNoiseReportDataEntry/view/speech_in_noise_report_data_entry_view.dart';
import '../../login/view/login_phone_view.dart';
import '../../ppg/view/ppg_reading_view.dart';
import '../../scanImages/controller/scan_images_controller.dart';
import '../../scanImages/view/scan_images_main_screen.dart';
import '../../temprature/view/temperature_reading_view.dart';
import '../../questionnaire/view/rhhi_questionnaire_view.dart';
import '../../questionnaire/view/aphab_questionnaire_view.dart';
import '../../questionnaire/view/tfi_questionnaire_view.dart';
import '../../questionnaire/view/cosi_questionnaire_view.dart';
import '../../questionnaire/view/vertigo_questionnaire_view.dart';

enum SubmitButtons {
  done("Done"),
  nextPatient("Next Patient"),
  overwrite("Overwrite");

  final String label;
  const SubmitButtons(this.label);
}

class MetaController extends GetxController {
  RxBool isLoading = true.obs;
  RxBool isNextPatientLoading = false.obs;
  RxBool isDoneButtonLoading = false.obs;
  RxBool isOverrideButtonLoading = false.obs;
  RxString patientName = ''.obs;
  MetaDataModel? metaDataModel;
  final ApiService _apiService = ApiService();
  final PrefsService _prefsService = PrefsService();
  final BleFlutterController _bleFlutterController =
      Get.find<BleFlutterController>();
  final PpgController _ppgController = Get.find<PpgController>();

  // Getter to expose BLE controller for UI access
  BleFlutterController get bleFlutterController => _bleFlutterController;

  final Map<String, String> routeToApiFieldMap = {
    BasicDetailView.routeName: 'basicDetails',
    BloodPressureReadingView.routeName: 'bpRecorded',
    AudiometryReportDataEntryView.routeName: 'audiometry',
    CognivueReportDataEntryView.routeName: 'cognivue',
    SpeechInNoiseReportDataEntryView.routeName: 'quickSin',
    ExtraPatientDataView.routeName: 'extraData',
    PpgReadingView.routeName: 'ppgRecorded',
    ScanImagesMainView.routeName: 'scanImages',
    TemperatureReadingView.routeName: 'temperatureRecorded',
    RHHIQuestionnaireView.routeName: 'RHHIQuest',
    APHABQuestionnaireView.routeName: 'APHABQuest',
    TFIQuestionnaireView.routeName: 'TFIQuest',
    COSIQuestionnaireView.routeName: 'COSIQuest',
    VertigoQuestionnaireView.routeName: 'VertigoQuest',
  };

  final List<String> scanImageFields =
      ScanImageType.values.map((e) => e.fieldName).toList();

  final RxMap<String, bool> moduleCompletionStatus = <String, bool>{
    BasicDetailView.routeName: false,
    BloodPressureReadingView.routeName: false,
    AudiometryReportDataEntryView.routeName: false,
    CognivueReportDataEntryView.routeName: false,
    SpeechInNoiseReportDataEntryView.routeName: false,
    ExtraPatientDataView.routeName: false,
    PpgReadingView.routeName: false,
    ScanImagesMainView.routeName: false,
    TemperatureReadingView.routeName: false,
    RHHIQuestionnaireView.routeName: false,
    APHABQuestionnaireView.routeName: false,
    TFIQuestionnaireView.routeName: false,
    COSIQuestionnaireView.routeName: false,
    VertigoQuestionnaireView.routeName: false,
  }.obs;

  @override
  void onInit() {
    getMetaData();
    loadPatientName();
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    // Refresh patient name when screen is ready
    refreshPatientName();
  }

  // Load patient name when controller initializes
  void loadPatientName() async {
    String fullName = await getPatientFullName();
    patientName.value = fullName;
  }

  // Refresh patient name (can be called when returning from other screens)
  void refreshPatientName() async {
    String fullName = await getPatientFullName();
    patientName.value = fullName;
  }

  Future<void> getMetaData() async {
    try {
      isLoading.value = true;
      final response = await _apiService.getMetaData();
      metaDataModel = response;
      if (metaDataModel != null && metaDataModel!.data.isNotEmpty) {
        updateModuleStatusFromApiResponse();
      } else {
        reusableSnackBar(message: "Something went wrong...");
      }
    } catch (e) {
      debugPrint("Error: $e");
      reusableSnackBar(message: "Something went wrong...");
    } finally {
      isLoading.value = false;
      update();
    }
  }

  bool isAllRequiredModulesMainCompleted() {
    // Create a map that excludes optional modules (audiometry, cognivue, speechInNoise, extraData, and questionnaires)
    final requiredModules = Map<String, bool>.from(moduleCompletionStatus)
      ..remove(AudiometryReportDataEntryView.routeName)
      ..remove(CognivueReportDataEntryView.routeName)
      ..remove(SpeechInNoiseReportDataEntryView.routeName)
      ..remove(ExtraPatientDataView.routeName)
      ..remove(RHHIQuestionnaireView.routeName)
      ..remove(APHABQuestionnaireView.routeName)
      ..remove(TFIQuestionnaireView.routeName)
      ..remove(COSIQuestionnaireView.routeName)
      ..remove(VertigoQuestionnaireView.routeName);

    // Check if all required modules are completed
    return requiredModules.values.every((isCompleted) => isCompleted);
  }

  Future<void> updateModuleStatusFromApiResponse() async {
    if (metaDataModel == null || metaDataModel!.data.isEmpty) {
      return;
    }

    moduleCompletionStatus.value[ScanImagesMainView.routeName] =
        await checkAllScanImagesCompleted();

    routeToApiFieldMap.forEach((routeName, apiField) {
      if (routeName != ScanImagesMainView.routeName) {
        bool isCompleted = false;
        for (var item in metaDataModel!.data) {
          final assessments = item.assessments;
          if (assessments.containsKey(apiField)) {
            isCompleted = assessments[apiField]!.completed;
            break;
          }
        }

        moduleCompletionStatus.value[routeName] = isCompleted;
      }
    });
    moduleCompletionStatus.refresh();
    update();
  }

  bool isScanImageCompleted(ScanImageType scanImageType) {
    if (metaDataModel == null || metaDataModel!.data.isEmpty) {
      return false;
    }

    String fieldName = scanImageType.fieldName;
    // Find the field in the data list
    for (var item in metaDataModel!.data) {
      if (item.assessments.containsKey(fieldName)) {
        return item.assessments[fieldName]!.completed;
      }
    }

    return false;
  }

  // Check if all scan image sections are completed
  Future<bool> checkAllScanImagesCompleted() async {
    if (metaDataModel == null) return false;
    // Using the new method to check each scan image type
    return ScanImageType.values.every((type) => isScanImageCompleted(type));
  }

  // Method to update module completion status
  void updateModuleStatus(String moduleName, bool status) {
    moduleCompletionStatus.value[moduleName] = status;
    checkAllScanImagesCompleted();
    moduleCompletionStatus.refresh();

    update();
  }

  // Method to navigate to a specific module
  void navigateToModule(String routeName) {
    Get.toNamed(routeName);
  }

  bool shouldShowModule(String routeName) {
    if (metaDataModel == null || metaDataModel!.data.isEmpty) {
      return false;
    }

    if (routeName == ScanImagesMainView.routeName) {
      return shouldShowScanImagesModule();
    }

    String? apiField = routeToApiFieldMap[routeName];
    if (apiField == null) return false;

    // Find the corresponding assessment in the data
    for (var item in metaDataModel!.data) {
      if (item.assessments.containsKey(apiField)) {
        return item.assessments[apiField]!.show;
      }
    }

    return false;
  }

  bool shouldShowScanImagesModule() {
    if (metaDataModel == null) return false;

    return ScanImageType.values.any((type) {
      String fieldName = type.fieldName;
      for (var item in metaDataModel!.data) {
        if (item.assessments.containsKey(fieldName) &&
            item.assessments[fieldName]!.show) {
          return true;
        }
      }
      return false;
    });
  }

  Future<Map<String, dynamic>?> postMetaCompletion(SubmitButtons button) async {
    try {
      final response = await _apiService.postMetaCompletion(
        buttonTitle: _toCamelCase(button.label),
      );

      // Check if response contains error and failedImages
      if (response != null && response.containsKey('error')) {
        if (response['error'] == 'Image quality is not good' &&
            response.containsKey('failedImages')) {
          return response;
        }
      }
      return response;
    } catch (e) {
      debugPrint('Error in postMetaCompletion: $e');
      return null;
    }
  }

  // String getButtonTitle() {
  //   if (isAllRequiredModulesMainCompleted()) {
  //     return metaDataModel?.isDone == true ? "Resubmit" : "Done";
  //   }
  //   return "Next Patient";
  // }

  void onButtonPressed(BuildContext context, SubmitButtons button) async {
    bool canNavigate = false;

    if (button == SubmitButtons.nextPatient) {
      isNextPatientLoading.value = true;
    } else if (button == SubmitButtons.done) {
      isDoneButtonLoading.value = true;
    } else if (button == SubmitButtons.overwrite) {
      isOverrideButtonLoading.value = true;
    }

    try {
      canNavigate = await _performButtonAction(context, button);
    } catch (e) {
      debugPrint('Error in onNextUserClicked: $e');
    } finally {
      if (button == SubmitButtons.nextPatient) {
        isNextPatientLoading.value = false;
      } else if (button == SubmitButtons.done) {
        isDoneButtonLoading.value = false;
      } else if (button == SubmitButtons.overwrite) {
        isOverrideButtonLoading.value = false;
      }

      if (canNavigate) {
        Get.offAllNamed(LoginPhoneView.routeName);
      }
    }
  }

  Future<bool> _performButtonAction(
      BuildContext context, SubmitButtons button) async {
    String? path;
    bool canShowSubmitButtonForPPG =
        _bleFlutterController.canShowSubmitButtonForPPG.value;

    if (canShowSubmitButtonForPPG) {
      path = await _ppgController.onMonitoringSubmitTap(context, shouldNavigateToMeta: true);
    }

    if (button == SubmitButtons.done) {
      LogEvents.logDoneGTEvent(folderName: path ?? "");
    } else if (button == SubmitButtons.nextPatient) {
      LogEvents.logNextPatientGTEvent(folderName: path ?? "");
    } else if (button == SubmitButtons.overwrite) {
      LogEvents.logDoneGTEvent(folderName: path ?? "");
    }

    final response = await postMetaCompletion(button);

    // If response is null, it might be due to a 460 error which has already shown a snackbar
    // So we should just return false to prevent navigation
    if (response == null) {
      return false;
    }

    // Skip image quality check for override button
    if (button != SubmitButtons.overwrite && response.containsKey('failedImages')) {
      final List<String> failedImages =
          List<String>.from(response['failedImages']);

      // Capitalize the failed image names properly
      final List<String> capitalizedFailedImages = failedImages
          .map(
              (imageName) => ImageNameHelper.getCapitalizedImageName(imageName))
          .toList();

      String errorMessage =
          'Image quality is not good for: ${capitalizedFailedImages.join(", ")}, Please rescan the images';
      await getMetaData();

      // Show the reusable dialog instead of snackbar
      _showImageQualityDialog(context, errorMessage, button);
      return false;
    }

    // Continue with normal flow if no image quality issues
    await _bleFlutterController.disconnectAndResetAll();
    await _prefsService.clearByKey(PrefsKeys.accessToken);
    await _prefsService.clearByKey(PrefsKeys.identifier);
    await _prefsService.clearByKey(PrefsKeys.refreshToken);
    await _prefsService.clearLoginInfo(); // Clear login-related information
    return true;
  }

  // New method to show the image quality dialog
  void _showImageQualityDialog(BuildContext context, String errorMessage, SubmitButtons originalButton) {
    ReusableDialog.show(
      isDismissible: false,
      borderRadius: BorderRadius.circular(8),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 24,
        vertical: 36,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.warning_amber_rounded,
            size: 60,
            color: Colors.orange,
          ),
          const SizedBox(
            height: 18,
          ),
          Text(
            errorMessage,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          Row(
            children: [
              Expanded(
                child: ReusableButton(
                  title: "Retake Images",
                  fontSize: 18,
                  color: Colors.grey,
                  borderColor: Colors.grey,
                  onTap: () {
                    ReusableDialog.close();
                    Get.offAllNamed(ScanImagesMainView.routeName);
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Obx(() => ReusableButton(
                  title: "Proceed Anyways",
                  fontSize: 18,
                  isLoading: isOverrideButtonLoading.value,
                  color: Colors.red,
                  borderColor: Colors.red,
                  onTap: () {
                    ReusableDialog.close();
                    onButtonPressed(context, SubmitButtons.overwrite);
                  },
                )),
              ),
            ],
          ),
          const SizedBox(
            height: 16,
          ),
        ],
      ),
    );
  }

  String _toCamelCase(String input) {
    if (input.isEmpty) return '';
    List<String> words = input.split(RegExp(r'[ _-]+'));
    if (words.isEmpty) return '';
    String firstWord = words.first.toLowerCase();
    String camelCase = words
        .skip(1)
        .map((word) => word.isNotEmpty
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : '')
        .join('');

    return firstWord + camelCase;
  }

  // Check if patient has already completed GT+
  bool hasPatientCompletedGTPlusOnce() {
    return metaDataModel?.isDone == true;
  }

  // Get notification message if patient has already completed GT+
  String? getCompletionNotificationMessage() {
    return "The patient has already completed the GT + once. Continue if you wish to do it again.";
  }

  // Get patient full name if available
  Future<String> getPatientFullName() async {
    try {
      // First check if name is available from stored preferences
      String patientName = await _prefsService.getPatientName();

      if (patientName.isNotEmpty) {
        return _capitalizeFullName(patientName);
      }

      // If not available from preferences, try to get from BasicDetails API
      final Map<String, dynamic>? response = await _apiService.getDetails(
        type: BasicDetailTypes.BasicDetails,
      );

      if (response != null) {
        final basicDetails = BasicDetailsModel.fromJson(response);
        if (basicDetails.firstName.isNotEmpty &&
            basicDetails.lastName.isNotEmpty) {
          String firstName = _capitalizeName(basicDetails.firstName);
          String lastName = _capitalizeName(basicDetails.lastName);
          return "$firstName $lastName";
        }
      }

      return "";
    } catch (e) {
      debugPrint("Error getting patient name: $e");
      return "";
    }
  }

  // Helper method to capitalize individual names
  String _capitalizeName(String name) {
    if (name.isEmpty) return name;
    return name[0].toUpperCase() + name.substring(1).toLowerCase();
  }

  // Helper method to capitalize full names (handles multiple words)
  String _capitalizeFullName(String fullName) {
    if (fullName.isEmpty) return fullName;
    return fullName
        .split(' ')
        .map((word) => _capitalizeName(word))
        .join(' ');
  }
}

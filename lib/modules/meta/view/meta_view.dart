import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/audiometryReportDataEntry/view/audiometry_report_data_entry_view.dart';
import 'package:gt_plus/modules/basicDetails/view/basic_detail_view.dart';
import 'package:gt_plus/modules/bloodPressure/view/blood_pressure_reading_view.dart';
import 'package:gt_plus/modules/meta/controller/meta_controller.dart';
import 'package:gt_plus/modules/ppg/view/ppg_reading_view.dart';
import 'package:gt_plus/modules/scanImages/view/scan_images_main_screen.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gt_plus/utils/appConst/app_images.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/modules/BleFlutter/ble_flutter_controller.dart';

import '../../../utils/reusableWidgets/reusable_button.dart';
import '../../../utils/reusableWidgets/reusable_footer.dart';
import '../../cognivueReportDataEntry/view/cognivue_report_data_entry_view.dart';
import '../../speechInNoiseReportDataEntry/view/speech_in_noise_report_data_entry_view.dart';
import '../../temprature/view/temperature_reading_view.dart';
import '../../extraPatientData/view/extra_patient_data_view.dart';
import '../../questionnaire/view/rhhi_questionnaire_view.dart';
import '../../questionnaire/view/aphab_questionnaire_view.dart';
import '../../questionnaire/view/tfi_questionnaire_view.dart';
import '../../questionnaire/view/cosi_questionnaire_view.dart';
import '../../questionnaire/view/vertigo_questionnaire_view.dart';

class MetaView extends GetView<MetaController> {
  const MetaView({super.key});

  static const routeName = "/MetaView";

  @override
  Widget build(BuildContext context) {
    // Refresh patient name when this screen is built (e.g., when returning from other screens)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.refreshPatientName();
    });

    return Scaffold(
      appBar: reusableAppBar(context: context, shouldGoBack: false),
      bottomNavigationBar: const ReusableFooter(),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: context.width * .05),
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }
          return ListView(
            children: [
              const SizedBox(height: 24),
              if (controller.hasPatientCompletedGTPlusOnce())
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.amber.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.amber.shade800),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          controller.getCompletionNotificationMessage() ?? "",
                          style: TextStyle(
                            color: Colors.amber.shade900,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              // Patient Name Display
              Obx(() {
                if (controller.patientName.value.isNotEmpty) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.person, color: Colors.blue.shade800, size: 24),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Patient Name",
                                style: TextStyle(
                                  color: Colors.blue.shade700,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                controller.patientName.value,
                                style: TextStyle(
                                  color: Colors.blue.shade900,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),
              _buildModuleVisibility(PpgReadingView.routeName, 'PPG'),
              _buildModuleVisibility(
                  BasicDetailView.routeName, 'Basic Details'),
              _buildModuleVisibility(
                  TemperatureReadingView.routeName, 'Temperature'),
              _buildModuleVisibility(
                  BloodPressureReadingView.routeName, 'Blood Pressure'),
              _buildModuleVisibility(ScanImagesMainView.routeName, 'Images'),
              _buildModuleVisibility(AudiometryReportDataEntryView.routeName,
                  'Audiometry Report Data Entry'),
              _buildModuleVisibility(CognivueReportDataEntryView.routeName,
                  'Cognitive Report Data Entry'),
              _buildModuleVisibility(SpeechInNoiseReportDataEntryView.routeName,
                  'Speech in Noise Report Data Entry'),
              _buildModuleVisibility(
                  ExtraPatientDataView.routeName, 'Extra Patient Data'),
              _buildModuleVisibility(RHHIQuestionnaireView.routeName,
                  'RHHI Questionnaire'),
              _buildModuleVisibility(APHABQuestionnaireView.routeName,
                  'APHAB Questionnaire'),
              _buildModuleVisibility(TFIQuestionnaireView.routeName,
                  'TFI Questionnaire'),
              _buildModuleVisibility(COSIQuestionnaireView.routeName,
                  'COSI Questionnaire'),
              _buildModuleVisibility(VertigoQuestionnaireView.routeName,
                  'Vertigo/Balance Questionnaire'),
              const SizedBox(height: 28),
              Center(
                child: Obx(() => Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ReusableButton(
                          title: SubmitButtons.nextPatient.label,
                          isLoading: controller.isNextPatientLoading.value,
                          width: 145,
                          radius: 10,
                          height: 50,
                          fontSize: 20,
                          color: Colors.red,
                          onTap: () {
                            controller.onButtonPressed(
                                context, SubmitButtons.nextPatient);
                          },
                        ),
                        const SizedBox(width: 16),
                        Visibility(
                          visible:
                              controller.isAllRequiredModulesMainCompleted(),
                          child: ReusableButton(
                            title: SubmitButtons.done.label,
                            isLoading: controller.isDoneButtonLoading.value,
                            width: 145,
                            radius: 10,
                            height: 50,
                            fontSize: 20,
                            borderColor: Colors.green,
                            color: Colors.green,
                            onTap: () {
                              controller.onButtonPressed(
                                  context, SubmitButtons.done);
                            },
                          ),
                        ),
                      ],
                    )),
              ),
              const SizedBox(height: 28), // Added spacing after buttons
            ],
          );
        }),
      ),
    );
  }

  Widget _buildModuleVisibility(String routeName, String displayName) {
    if (!controller.shouldShowModule(routeName)) {
      return const SizedBox.shrink();
    }

    String iconPath = '';

    if (routeName == PpgReadingView.routeName) {
      iconPath = AppImages.icPpg;
    } else if (routeName == BasicDetailView.routeName) {
      iconPath = AppImages.icBasicDetail;
    } else if (routeName == TemperatureReadingView.routeName) {
      iconPath = AppImages.icTemperature;
    } else if (routeName == BloodPressureReadingView.routeName) {
      iconPath = AppImages.icBp;
    } else if (routeName == ScanImagesMainView.routeName) {
      iconPath = AppImages.icScanImage;
    } else if (routeName == AudiometryReportDataEntryView.routeName) {
      iconPath = AppImages.icAudiometry;
    } else if (routeName == CognivueReportDataEntryView.routeName) {
      iconPath = AppImages.icCognitive;
    } else if (routeName == SpeechInNoiseReportDataEntryView.routeName) {
      iconPath = AppImages.icAudiometry;
    } else if (routeName == ExtraPatientDataView.routeName) {
      iconPath = AppImages.icBasicDetail;
    } else if (routeName == RHHIQuestionnaireView.routeName) {
      iconPath = AppImages.icAudiometry; // Using audiometry icon as placeholder
    } else if (routeName == APHABQuestionnaireView.routeName) {
      iconPath = AppImages.icAudiometry; // Using audiometry icon as placeholder
    } else if (routeName == TFIQuestionnaireView.routeName) {
      iconPath = AppImages.icAudiometry; // Using audiometry icon as placeholder
    } else if (routeName == COSIQuestionnaireView.routeName) {
      iconPath = AppImages.icAudiometry; // Using audiometry icon as placeholder
    } else if (routeName == VertigoQuestionnaireView.routeName) {
      iconPath = AppImages.icAudiometry; // Using audiometry icon as placeholder
    }

    return Obx(() => _buildModuleCard(
          Get.context!,
          displayName,
          routeName,
          iconPath,
        ));
  }

  Widget _buildModuleCard(
    BuildContext context,
    String moduleName,
    String routeName,
    String iconPath,
  ) {
    final isCompleted =
        controller.moduleCompletionStatus.value[routeName] ?? false;

    // Check if this is the PPG module and if timer is running
    final isPpgModule = routeName == PpgReadingView.routeName;

    return Card(
      color: Colors.white,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListTile(
        leading: iconPath.isNotEmpty
            ? SvgPicture.asset(iconPath, width: 28, height: 28)
            : null,
        title: isPpgModule ? _buildPpgTitleWithTimer(moduleName) : Text(
          moduleName,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isCompleted)
              const Icon(
                Icons.check_circle,
                color: Colors.green,
              ),
            IconButton(
              icon: Container(
                height: 32,
                width: 32,
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: Colors.deepOrange,
                  size: 20,
                ),
              ),
              onPressed: () => controller.navigateToModule(routeName),
            ),
          ],
        ),
        onTap: () => controller.navigateToModule(routeName),
      ),
    );
  }

  Widget _buildPpgTitleWithTimer(String moduleName) {
    try {
      final bleController = Get.find<BleFlutterController>();

      return Obx(() {
        // Only observe the specific reactive variable we need
        final remainingTime = bleController.remainingSeconds.value;
        final hasActiveTimer = bleController.timer?.isActive == true;
        final isTimerRunning = hasActiveTimer && remainingTime > 0;

        return Row(
          children: [
            Text(
              moduleName,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (isTimerRunning) ...[
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.orange.withValues(alpha: 0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _formatRemainingTime(remainingTime),
                      style: const TextStyle(
                        color: AppColors.darkTeal,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        );
      });
    } catch (e) {
      // If BLE controller is not found or any error occurs, return just the module name
      return Text(
        moduleName,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      );
    }
  }

  String _formatRemainingTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}

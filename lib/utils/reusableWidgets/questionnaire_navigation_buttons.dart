import 'package:flutter/material.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';

class QuestionnaireNavigationButtons extends StatelessWidget {
  final bool hasPrevious;
  final bool hasNext;
  final VoidCallback? onPrevious;
  final VoidCallback? onNext;
  final VoidCallback? onSubmit;
  final bool isSubmitting;
  final bool isReadOnly;
  final bool showSubmit;

  const QuestionnaireNavigationButtons({
    super.key,
    required this.hasPrevious,
    required this.hasNext,
    this.onPrevious,
    this.onNext,
    this.onSubmit,
    this.isSubmitting = false,
    this.isReadOnly = false,
    this.showSubmit = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isReadOnly) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // Previous button
          if (hasPrevious)
            Expanded(
              child: ReusableButton(
                title: 'Previous',
                onTap: onPrevious,
                color: Colors.grey.shade600,
                borderColor: Colors.grey.shade600,
                height: 44,
                fontSize: 15,
              ),
            )
          else
            const Expanded(child: SizedBox()),

          const SizedBox(width: 12),

          // Next or Submit button
          Expanded(
            child: showSubmit
                ? ReusableButton(
                    title: 'Submit',
                    onTap: onSubmit,
                    isLoading: isSubmitting,
                    color: AppColors.charcoalBlue,
                    borderColor: AppColors.charcoalBlue,
                    height: 44,
                    fontSize: 15,
                  )
                : ReusableButton(
                    title: hasNext ? 'Next' : 'Finish',
                    onTap: hasNext ? onNext : onSubmit,
                    isLoading: !hasNext && isSubmitting,
                    color: AppColors.charcoalBlue,
                    borderColor: AppColors.charcoalBlue,
                    height: 44,
                    fontSize: 15,
                  ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';

class QuestionnaireProgressIndicator extends StatelessWidget {
  final double progressPercentage;
  final String currentProgress;
  final String title;

  const QuestionnaireProgressIndicator({
    super.key,
    required this.progressPercentage,
    required this.currentProgress,
    this.title = 'Progress',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            blurRadius: 1,
            offset: const Offset(0, 0.5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: AppColors.charcoalBlue,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                currentProgress,
                style: const TextStyle(
                  color: AppColors.charcoalBlue,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: progressPercentage,
                  backgroundColor: Colors.grey.shade200,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getProgressColor(progressPercentage),
                  ),
                  minHeight: 4,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 6),
              Text(
                '${(progressPercentage * 100).toInt()}%',
                style: TextStyle(
                  color: _getProgressColor(progressPercentage),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getProgressColor(double progress) {
    if (progress < 0.3) {
      return Colors.red.shade400;
    } else if (progress < 0.7) {
      return Colors.orange.shade400;
    } else {
      return Colors.green.shade400;
    }
  }
}

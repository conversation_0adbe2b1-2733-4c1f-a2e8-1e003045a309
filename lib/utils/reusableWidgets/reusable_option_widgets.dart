import 'package:flutter/material.dart';

/// Reusable radio button option widget
class ReusableRadioOption extends StatelessWidget {
  final String label;
  final String value;
  final String? selectedValue;
  final bool isReadOnly;
  final VoidCallback? onTap;
  final bool useWrapLayout;

  const ReusableRadioOption({
    super.key,
    required this.label,
    required this.value,
    required this.selectedValue,
    this.isReadOnly = false,
    this.onTap,
    this.useWrapLayout = true,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = selectedValue == value;
    
    if (useWrapLayout) {
      return GestureDetector(
        onTap: isReadOnly ? null : onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: isReadOnly
                ? Colors.grey.shade200
                : (isSelected ? Colors.blue.shade100 : Colors.grey.shade100),
            border: Border.all(
              color: isReadOnly
                  ? Colors.grey.shade400
                  : (isSelected ? Colors.blue : Colors.grey.shade300),
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                size: 18,
                color: isReadOnly
                    ? Colors.grey.shade500
                    : (isSelected ? Colors.blue : Colors.grey),
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: isReadOnly
                      ? Colors.grey.shade600
                      : (isSelected ? Colors.blue.shade800 : Colors.black87),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // Column layout for better alignment (like APHAB)
      return Container(
        width: double.infinity,
        margin: const EdgeInsets.only(bottom: 8),
        child: GestureDetector(
          onTap: isReadOnly ? null : onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isReadOnly
                  ? Colors.grey.shade200
                  : (isSelected ? Colors.blue.shade100 : Colors.grey.shade100),
              border: Border.all(
                color: isReadOnly
                    ? Colors.grey.shade400
                    : (isSelected ? Colors.blue : Colors.grey.shade300),
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                  size: 20,
                  color: isReadOnly
                      ? Colors.grey.shade500
                      : (isSelected ? Colors.blue : Colors.grey),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    label,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: isReadOnly
                          ? Colors.grey.shade600
                          : (isSelected ? Colors.blue.shade800 : Colors.black87),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
  }
}

/// Reusable checkbox option widget
class ReusableCheckboxOption extends StatelessWidget {
  final String label;
  final String value;
  final List<String> selectedValues;
  final bool isReadOnly;
  final VoidCallback? onTap;

  const ReusableCheckboxOption({
    super.key,
    required this.label,
    required this.value,
    required this.selectedValues,
    this.isReadOnly = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = selectedValues.contains(value);
    
    return GestureDetector(
      onTap: isReadOnly ? null : onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isReadOnly
              ? Colors.grey.shade200
              : (isSelected ? Colors.blue.shade100 : Colors.grey.shade100),
          border: Border.all(
            color: isReadOnly
                ? Colors.grey.shade400
                : (isSelected ? Colors.blue : Colors.grey.shade300),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isSelected ? Icons.check_box : Icons.check_box_outline_blank,
              size: 18,
              color: isReadOnly
                  ? Colors.grey.shade500
                  : (isSelected ? Colors.blue : Colors.grey),
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: isReadOnly
                    ? Colors.grey.shade600
                    : (isSelected ? Colors.blue.shade800 : Colors.black87),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Reusable option group widget for radio buttons
class ReusableRadioGroup extends StatelessWidget {
  final List<String> labels;
  final List<String> values;
  final String? selectedValue;
  final bool isReadOnly;
  final Function(String) onChanged;
  final bool useWrapLayout;

  const ReusableRadioGroup({
    super.key,
    required this.labels,
    required this.values,
    required this.selectedValue,
    this.isReadOnly = false,
    required this.onChanged,
    this.useWrapLayout = true,
  });

  @override
  Widget build(BuildContext context) {
    assert(labels.length == values.length, 'Labels and values must have the same length');
    
    if (useWrapLayout) {
      return Wrap(
        spacing: 8.0,
        runSpacing: 8.0,
        children: List.generate(labels.length, (index) {
          return ReusableRadioOption(
            label: labels[index],
            value: values[index],
            selectedValue: selectedValue,
            isReadOnly: isReadOnly,
            useWrapLayout: true,
            onTap: () => onChanged(values[index]),
          );
        }),
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(labels.length, (index) {
          return ReusableRadioOption(
            label: labels[index],
            value: values[index],
            selectedValue: selectedValue,
            isReadOnly: isReadOnly,
            useWrapLayout: false,
            onTap: () => onChanged(values[index]),
          );
        }),
      );
    }
  }
}

/// Reusable option group widget for checkboxes
class ReusableCheckboxGroup extends StatelessWidget {
  final List<String> labels;
  final List<String> values;
  final List<String> selectedValues;
  final bool isReadOnly;
  final Function(List<String>) onChanged;

  const ReusableCheckboxGroup({
    super.key,
    required this.labels,
    required this.values,
    required this.selectedValues,
    this.isReadOnly = false,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    assert(labels.length == values.length, 'Labels and values must have the same length');
    
    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: List.generate(labels.length, (index) {
        return ReusableCheckboxOption(
          label: labels[index],
          value: values[index],
          selectedValues: selectedValues,
          isReadOnly: isReadOnly,
          onTap: () {
            final currentValues = List<String>.from(selectedValues);
            if (currentValues.contains(values[index])) {
              currentValues.remove(values[index]);
            } else {
              currentValues.add(values[index]);
            }
            onChanged(currentValues);
          },
        );
      }),
    );
  }
}
